"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hala-app/dashboard/page",{

/***/ "(app-pages-browser)/./app/hala-app/dashboard/page.tsx":
/*!*****************************************!*\
  !*** ./app/hala-app/dashboard/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_page_transition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/page-transition */ \"(app-pages-browser)/./components/page-transition.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchUserStats();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchUserStats = async ()=>{\n        try {\n            // Import Edge Functions client\n            const { edgeFunctions } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_edge-functions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/edge-functions */ \"(app-pages-browser)/./lib/edge-functions.ts\"));\n            const data = await edgeFunctions.getMyItems();\n            if (data.success) {\n                // Calculate user stats from items\n                const items = data.nfts || [];\n                setStats({\n                    totalItems: items.length,\n                    tokensCount: items.length,\n                    totalUsers: 1,\n                    businessUsers: items.filter((item)=>item.brand).length,\n                    totalTransfers: 0,\n                    totalValue: 0,\n                    recentActivity: [],\n                    marketOverview: []\n                });\n            } else {\n                throw new Error(data.error || \"Failed to fetch dashboard stats\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching dashboard stats:\", error);\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to load dashboard data\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_3__.PageTransition, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    var _stats_totalUsers, _stats_tokensCount, _stats_businessUsers;\n    const dynamicStats = [\n        {\n            name: \"Total Users\",\n            value: ((_stats_totalUsers = stats === null || stats === void 0 ? void 0 : stats.totalUsers) !== null && _stats_totalUsers !== void 0 ? _stats_totalUsers : 0).toLocaleString(),\n            change: \"+12%\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 13\n            }, this),\n            color: \"bg-blue-50\"\n        },\n        {\n            name: \"Your Tokens\",\n            value: ((_stats_tokensCount = stats === null || stats === void 0 ? void 0 : stats.tokensCount) !== null && _stats_tokensCount !== void 0 ? _stats_tokensCount : 0).toLocaleString(),\n            change: \"+23%\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5 text-purple-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, this),\n            color: \"bg-purple-50\"\n        },\n        {\n            name: \"Active Projects\",\n            value: ((_stats_businessUsers = stats === null || stats === void 0 ? void 0 : stats.businessUsers) !== null && _stats_businessUsers !== void 0 ? _stats_businessUsers : 0).toLocaleString(),\n            change: \"+7%\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5 text-green-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, this),\n            color: \"bg-green-50\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_3__.PageTransition, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 md:space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl md:text-3xl font-light\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 mt-2 text-sm md:text-base\",\n                            children: \"Welcome to your HALA dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6\",\n                    children: dynamicStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"p-4 md:p-6 hover:shadow-lg transition-all duration-300 overflow-hidden relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-full \".concat(stat.color),\n                                            children: stat.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-500 flex items-center text-xs md:text-sm bg-green-50 px-2 py-1 rounded-full\",\n                                            children: [\n                                                stat.change,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3 md:h-4 md:w-4 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 md:mt-4 relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-gray-500 text-xs md:text-sm\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl md:text-3xl font-light mt-1\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"p-4 md:p-6 overflow-hidden relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg md:text-xl font-light\",\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-gray-50 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 md:space-y-4\",\n                                        children: (stats === null || stats === void 0 ? void 0 : stats.recentActivity) && stats.recentActivity.length > 0 ? stats.recentActivity.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between py-2 md:py-3 border-b border-gray-100 last:border-0 hover:bg-gray-50 rounded-lg transition-colors px-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs md:text-sm text-gray-900 font-medium\",\n                                                                children: item.nftName || \"Token #\".concat(item.nftId.toString().substring(0, 6))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(item.transferredAt).toLocaleString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs md:text-sm text-gray-600 font-medium hover:text-black transition-colors\",\n                                                        children: item.senderId.toString() === item.recipientId.toString() ? \"Created\" : \"Transferred\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 21\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4 text-gray-500\",\n                                            children: \"No recent activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"p-4 md:p-6 overflow-hidden relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg md:text-xl font-light\",\n                                                children: \"Market Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-gray-50 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-32 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 flex items-end justify-between h-full\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.marketOverview) && stats.marketOverview.length > 0 ? stats.marketOverview.slice(0, 7).map((day, index)=>{\n                                                        const maxValue = Math.max(...stats.marketOverview.map((d)=>d.count), 1);\n                                                        const heightPercentage = day.count / maxValue * 100;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1/8 mx-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-black rounded-t-sm transition-all duration-500 hover:bg-gray-800\",\n                                                                style: {\n                                                                    height: \"\".concat(heightPercentage || 5, \"%\")\n                                                                },\n                                                                title: \"\".concat(day.date, \": \").concat(day.count, \" NFTs\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 29\n                                                        }, this);\n                                                    }) : [\n                                                        35,\n                                                        60,\n                                                        45,\n                                                        75,\n                                                        50,\n                                                        80,\n                                                        65\n                                                    ].map((height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1/8 mx-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-t-sm\",\n                                                                style: {\n                                                                    height: \"\".concat(height, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-400 pt-2 border-t border-gray-100\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.marketOverview) && stats.marketOverview.length > 0 ? stats.marketOverview.slice(0, 7).map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: new Date(day.date).toLocaleDateString(undefined, {\n                                                                weekday: \"short\"\n                                                            })\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 29\n                                                        }, this)) : [\n                                                        \"Mon\",\n                                                        \"Tue\",\n                                                        \"Wed\",\n                                                        \"Thu\",\n                                                        \"Fri\",\n                                                        \"Sat\",\n                                                        \"Sun\"\n                                                    ].map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: day\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"JPgYiHvdyXuc34iT/chz3KVr3e0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/hala-app/dashboard/page.tsx\n"));

/***/ })

});