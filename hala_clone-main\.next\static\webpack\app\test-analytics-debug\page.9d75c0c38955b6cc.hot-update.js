"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-analytics-debug/page",{

/***/ "(app-pages-browser)/./app/test-analytics-debug/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-analytics-debug/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAnalyticsDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestAnalyticsDebug() {\n    var _authState_user, _authState_user1;\n    _s();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analyticsResult, setAnalyticsResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestAnalyticsDebug.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"TestAnalyticsDebug.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            setAuthState(session);\n            if (session) {\n                // Get user profile\n                const { data: profileData, error } = await supabase.from('profiles').select('*').eq('id', session.user.id).single();\n                console.log('Profile query result:', {\n                    profileData,\n                    error\n                });\n                if (error) {\n                    console.error('Profile error:', error);\n                    // If profile doesn't exist, show the error\n                    setProfile({\n                        error: error.message\n                    });\n                } else {\n                    setProfile(profileData);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n        }\n    };\n    const testAnalytics = async ()=>{\n        setLoading(true);\n        try {\n            console.log('Testing analytics function...');\n            // Get current session\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            if (!session) {\n                setAnalyticsResult({\n                    error: 'No session found'\n                });\n                return;\n            }\n            console.log('Session found, testing direct fetch...');\n            // Test direct fetch to Edge Function\n            const response = await fetch(\"\".concat(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"/functions/v1/dashboard-analytics?timeRange=30d\"), {\n                method: 'GET',\n                headers: {\n                    'Authorization': \"Bearer \".concat(session.access_token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log('Response status:', response.status);\n            console.log('Response headers:', Object.fromEntries(response.headers.entries()));\n            const result = await response.json();\n            console.log('Response data:', result);\n            setAnalyticsResult({\n                status: response.status,\n                ok: response.ok,\n                data: result\n            });\n        } catch (error) {\n            console.error('Analytics test error:', error);\n            setAnalyticsResult({\n                error: error.message\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                console.error('Login error:', error);\n            } else {\n                console.log('Login successful:', data);\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Analytics Debug Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Authentication Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Authenticated:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    authState ? 'Yes' : 'No'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"User ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user = authState.user) === null || _authState_user === void 0 ? void 0 : _authState_user.id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user1 = authState.user) === null || _authState_user1 === void 0 ? void 0 : _authState_user1.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Access Token:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            authState.access_token ? 'Present' : 'Missing'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    !authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: login,\n                        className: \"mt-4\",\n                        children: \"Login with Test Credentials\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"User Profile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Role:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.role\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Full Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.full_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Business Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.business_name || 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Analytics Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: testAnalytics,\n                        disabled: loading || !authState,\n                        className: \"mb-4\",\n                        children: loading ? 'Testing...' : 'Test Analytics Function'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    analyticsResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold mb-2\",\n                                children: \"Result:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-4 rounded text-sm overflow-auto\",\n                                children: JSON.stringify(analyticsResult, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAnalyticsDebug, \"qrLBCFpXGumtBD54+W+vEyxYn7g=\");\n_c = TestAnalyticsDebug;\nvar _c;\n$RefreshReg$(_c, \"TestAnalyticsDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-analytics-debug/page.tsx\n"));

/***/ })

});