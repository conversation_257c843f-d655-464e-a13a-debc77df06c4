"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hala-app/dashboard/page",{

/***/ "(app-pages-browser)/./app/hala-app/dashboard/page.tsx":
/*!*****************************************!*\
  !*** ./app/hala-app/dashboard/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowUpRight,BarChart3,Coins,Loader2,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _components_page_transition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/page-transition */ \"(app-pages-browser)/./components/page-transition.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchUserStats();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchUserStats = async ()=>{\n        try {\n            // Import Edge Functions client\n            const { edgeFunctions } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_edge-functions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/edge-functions */ \"(app-pages-browser)/./lib/edge-functions.ts\"));\n            const data = await edgeFunctions.getMyItems();\n            if (data.success) {\n                // Calculate user stats from items\n                const items = data.nfts || [];\n                setStats({\n                    totalItems: items.length,\n                    totalTransfers: 0,\n                    totalValue: 0,\n                    recentActivity: []\n                });\n            } else {\n                throw new Error(data.error || \"Failed to fetch dashboard stats\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching dashboard stats:\", error);\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to load dashboard data\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_3__.PageTransition, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    const dynamicStats = [\n        {\n            name: \"Total Users\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalUsers.toLocaleString()) || \"0\",\n            change: \"+12%\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5 text-blue-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, this),\n            color: \"bg-blue-50\"\n        },\n        {\n            name: \"Your Tokens\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.tokensCount.toLocaleString()) || \"0\",\n            change: \"+23%\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5 text-purple-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 13\n            }, this),\n            color: \"bg-purple-50\"\n        },\n        {\n            name: \"Active Projects\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.businessUsers.toLocaleString()) || \"0\",\n            change: \"+7%\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5 text-green-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, this),\n            color: \"bg-green-50\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_3__.PageTransition, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6 md:space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl md:text-3xl font-light\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 mt-2 text-sm md:text-base\",\n                            children: \"Welcome to your HALA dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6\",\n                    children: dynamicStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"p-4 md:p-6 hover:shadow-lg transition-all duration-300 overflow-hidden relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-full \".concat(stat.color),\n                                            children: stat.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-500 flex items-center text-xs md:text-sm bg-green-50 px-2 py-1 rounded-full\",\n                                            children: [\n                                                stat.change,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3 md:h-4 md:w-4 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 md:mt-4 relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-gray-500 text-xs md:text-sm\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl md:text-3xl font-light mt-1\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"p-4 md:p-6 overflow-hidden relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg md:text-xl font-light\",\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-gray-50 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 md:space-y-4\",\n                                        children: (stats === null || stats === void 0 ? void 0 : stats.recentActivity) && stats.recentActivity.length > 0 ? stats.recentActivity.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between py-2 md:py-3 border-b border-gray-100 last:border-0 hover:bg-gray-50 rounded-lg transition-colors px-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs md:text-sm text-gray-900 font-medium\",\n                                                                children: item.nftName || \"Token #\".concat(item.nftId.toString().substring(0, 6))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(item.transferredAt).toLocaleString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs md:text-sm text-gray-600 font-medium hover:text-black transition-colors\",\n                                                        children: item.senderId.toString() === item.recipientId.toString() ? \"Created\" : \"Transferred\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 21\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4 text-gray-500\",\n                                            children: \"No recent activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"p-4 md:p-6 overflow-hidden relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg md:text-xl font-light\",\n                                                children: \"Market Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-gray-50 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowUpRight_BarChart3_Coins_Loader2_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-32 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 flex items-end justify-between h-full\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.marketOverview) && stats.marketOverview.length > 0 ? stats.marketOverview.slice(0, 7).map((day, index)=>{\n                                                        const maxValue = Math.max(...stats.marketOverview.map((d)=>d.count), 1);\n                                                        const heightPercentage = day.count / maxValue * 100;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1/8 mx-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-black rounded-t-sm transition-all duration-500 hover:bg-gray-800\",\n                                                                style: {\n                                                                    height: \"\".concat(heightPercentage || 5, \"%\")\n                                                                },\n                                                                title: \"\".concat(day.date, \": \").concat(day.count, \" NFTs\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 29\n                                                        }, this);\n                                                    }) : [\n                                                        35,\n                                                        60,\n                                                        45,\n                                                        75,\n                                                        50,\n                                                        80,\n                                                        65\n                                                    ].map((height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1/8 mx-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-t-sm\",\n                                                                style: {\n                                                                    height: \"\".concat(height, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-400 pt-2 border-t border-gray-100\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.marketOverview) && stats.marketOverview.length > 0 ? stats.marketOverview.slice(0, 7).map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: new Date(day.date).toLocaleDateString(undefined, {\n                                                                weekday: \"short\"\n                                                            })\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 29\n                                                        }, this)) : [\n                                                        \"Mon\",\n                                                        \"Tue\",\n                                                        \"Wed\",\n                                                        \"Thu\",\n                                                        \"Fri\",\n                                                        \"Sat\",\n                                                        \"Sun\"\n                                                    ].map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: day\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"JPgYiHvdyXuc34iT/chz3KVr3e0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/hala-app/dashboard/page.tsx\n"));

/***/ })

});