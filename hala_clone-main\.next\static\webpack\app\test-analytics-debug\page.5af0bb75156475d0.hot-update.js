"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-analytics-debug/page",{

/***/ "(app-pages-browser)/./app/test-analytics-debug/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-analytics-debug/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAnalyticsDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestAnalyticsDebug() {\n    var _authState_user, _authState_user1;\n    _s();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analyticsResult, setAnalyticsResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestAnalyticsDebug.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"TestAnalyticsDebug.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            setAuthState(session);\n            if (session) {\n                // Get user profile\n                const { data: profileData, error } = await supabase.from('profiles').select('*').eq('id', session.user.id).single();\n                console.log('Profile query result:', {\n                    profileData,\n                    error\n                });\n                if (error) {\n                    console.error('Profile error:', error);\n                    // If profile doesn't exist, show the error\n                    setProfile({\n                        error: error.message\n                    });\n                } else {\n                    setProfile(profileData);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n        }\n    };\n    const testAnalytics = async ()=>{\n        setLoading(true);\n        try {\n            console.log('Testing analytics function...');\n            // Get current session\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            if (!session) {\n                setAnalyticsResult({\n                    error: 'No session found'\n                });\n                return;\n            }\n            console.log('Session found, testing direct fetch...');\n            // Test direct fetch to Edge Function\n            const response = await fetch(\"\".concat(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"/functions/v1/dashboard-analytics?timeRange=30d\"), {\n                method: 'GET',\n                headers: {\n                    'Authorization': \"Bearer \".concat(session.access_token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log('Response status:', response.status);\n            console.log('Response headers:', Object.fromEntries(response.headers.entries()));\n            const result = await response.json();\n            console.log('Response data:', result);\n            setAnalyticsResult({\n                status: response.status,\n                ok: response.ok,\n                data: result\n            });\n        } catch (error) {\n            console.error('Analytics test error:', error);\n            setAnalyticsResult({\n                error: error.message\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                console.error('Login error:', error);\n            } else {\n                console.log('Login successful:', data);\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n        }\n    };\n    const createProfile = async ()=>{\n        try {\n            if (!(authState === null || authState === void 0 ? void 0 : authState.user)) {\n                alert('Please login first');\n                return;\n            }\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.from('profiles').insert({\n                id: authState.user.id,\n                email: authState.user.email || '<EMAIL>',\n                full_name: 'Test Business User',\n                role: 'business',\n                business_name: 'Test Business',\n                business_vat_number: '123456789'\n            }).select().single();\n            if (error) {\n                console.error('Profile creation error:', error);\n                alert(\"Error creating profile: \".concat(error.message));\n            } else {\n                console.log('Profile created:', data);\n                alert('Profile created successfully!');\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Profile creation error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Analytics Debug Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Authentication Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Authenticated:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    authState ? 'Yes' : 'No'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"User ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user = authState.user) === null || _authState_user === void 0 ? void 0 : _authState_user.id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user1 = authState.user) === null || _authState_user1 === void 0 ? void 0 : _authState_user1.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Access Token:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            authState.access_token ? 'Present' : 'Missing'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    !authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: login,\n                        className: \"mt-4\",\n                        children: \"Login with Test Credentials\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"User Profile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Role:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.role\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Full Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.full_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Business Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.business_name || 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Analytics Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: testAnalytics,\n                        disabled: loading || !authState,\n                        className: \"mb-4\",\n                        children: loading ? 'Testing...' : 'Test Analytics Function'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    analyticsResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold mb-2\",\n                                children: \"Result:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-4 rounded text-sm overflow-auto\",\n                                children: JSON.stringify(analyticsResult, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAnalyticsDebug, \"qrLBCFpXGumtBD54+W+vEyxYn7g=\");\n_c = TestAnalyticsDebug;\nvar _c;\n$RefreshReg$(_c, \"TestAnalyticsDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-analytics-debug/page.tsx\n"));

/***/ })

});