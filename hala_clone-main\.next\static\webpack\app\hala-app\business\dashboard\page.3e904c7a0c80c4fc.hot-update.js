"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hala-app/business/dashboard/page",{

/***/ "(app-pages-browser)/./app/hala-app/business/dashboard/page.tsx":
/*!**************************************************!*\
  !*** ./app/hala-app/business/dashboard/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BusinessDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_page_transition__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/page-transition */ \"(app-pages-browser)/./components/page-transition.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction BusinessDashboard() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BusinessDashboard.useEffect\": ()=>{\n            fetchBusinessStats();\n        }\n    }[\"BusinessDashboard.useEffect\"], []);\n    const fetchBusinessStats = async ()=>{\n        try {\n            // Import Edge Functions client\n            const { edgeFunctions } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_edge-functions_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/edge-functions */ \"(app-pages-browser)/./lib/edge-functions.ts\"));\n            const data = await edgeFunctions.getAnalytics('30d');\n            if (data.success) {\n                // Map analytics data to stats format for backward compatibility\n                const analytics = data.analytics;\n                setStats({\n                    totalItems: analytics.itemsSold || 0,\n                    totalTransfers: analytics.itemsSold || 0,\n                    totalCustomers: analytics.newCustomers || 0,\n                    totalRevenue: analytics.totalRevenue || 0\n                });\n            } else {\n                throw new Error(data.error || \"Failed to fetch business stats\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching business stats:\", error);\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to load business data\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshData = ()=>{\n        setIsRefreshing(true);\n        fetchBusinessStats().finally(()=>{\n            setIsRefreshing(false);\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_6__.PageTransition, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_6__.PageTransition, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl md:text-3xl font-light\",\n                                    children: \"Business Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mt-2 text-sm md:text-base\",\n                                    children: \"Monitor the performance of your tokenized assets\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: refreshData,\n                            disabled: isRefreshing,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2 \".concat(isRefreshing ? \"animate-spin\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                    defaultValue: \"overview\",\n                    className: \"space-y-6\",\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"trades\",\n                                    children: \"Trades\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"trends\",\n                                    children: \"Trends\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"overview\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        {\n                                            name: \"Tokenized Items\",\n                                            value: (stats === null || stats === void 0 ? void 0 : stats.tokenizedItems.toString()) || \"0\",\n                                            change: \"+12%\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 25\n                                            }, this),\n                                            color: \"bg-blue-50\"\n                                        },\n                                        {\n                                            name: \"Active Customers\",\n                                            value: (stats === null || stats === void 0 ? void 0 : stats.activeCustomers.toString()) || \"0\",\n                                            change: \"+23%\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 25\n                                            }, this),\n                                            color: \"bg-purple-50\"\n                                        },\n                                        {\n                                            name: \"Total Value\",\n                                            value: \"$0\",\n                                            change: \"+18%\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 25\n                                            }, this),\n                                            color: \"bg-green-50\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-full \".concat(stat.color),\n                                                            children: stat.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 flex items-center text-xs bg-green-50 px-2 py-1 rounded-full\",\n                                                            children: [\n                                                                stat.change,\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 37\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: stat.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-light mt-1\",\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"Recent Trades\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-gray-50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.recentTrades) && stats.recentTrades.length > 0 ? stats.recentTrades.map((tx, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between py-3 border-b border-gray-100 last:border-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: tx.nftName || \"Token #\".concat(tx.nftId.toString().substring(0, 6))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 158,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(tx.transferredAt).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 161,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"$1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 23\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-4 text-gray-500\",\n                                                        children: \"No recent trades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-4 border-t border-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                        href: \"/hala-app/business/trades\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            children: \"View all trades\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"Geographic Distribution\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-gray-50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-64 flex items-center justify-center bg-gray-50 rounded-lg mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-300 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"Trade distribution map\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Europe\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"42%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"North America\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"35%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Asia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"23%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"trades\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Number of Trades\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 7 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 30 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"default\",\n                                                        size: \"sm\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Trades\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Last 7 days:\",\n                                                                        \" \",\n                                                                        Math.floor(Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0) * 0.3)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Last 30 days:\",\n                                                                        \" \",\n                                                                        Math.floor(Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0) * 0.7)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-8xl font-light mb-2\",\n                                                    children: Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"real-time updated number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"trends\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Price Trends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 7 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 30 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"default\",\n                                                        size: \"sm\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Price Paid\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: [\n                                                            \"$\",\n                                                            Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 flex items-center justify-center bg-gray-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-300 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Price trend chart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Min price: $18\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Avg price: $21\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Max price: $24\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 pt-4 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium mb-3\",\n                                                children: \"Most Traded Collections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            (stats === null || stats === void 0 ? void 0 : stats.topCollections) && stats.topCollections.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: stats.topCollections.map((collection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            index === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Most traded: \"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-500\",\n                                                                                children: [\n                                                                                    index + 1,\n                                                                                    \". \"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: index === 0 ? \"font-medium\" : \"\",\n                                                                                children: collection.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 304,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium\",\n                                                                        children: [\n                                                                            collection.count,\n                                                                            \" trades (\",\n                                                                            collection.percentage,\n                                                                            \"%)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"\".concat(index === 0 ? \"bg-black\" : \"bg-gray-400\", \" h-1 rounded-full\"),\n                                                                    style: {\n                                                                        width: \"\".concat(collection.percentage, \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4 text-gray-500\",\n                                                children: \"No collection data available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessDashboard, \"zqbmixdx5KIbCjX8+LAZ4Ywz+FM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BusinessDashboard;\nvar _c;\n$RefreshReg$(_c, \"BusinessDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/hala-app/business/dashboard/page.tsx\n"));

/***/ })

});