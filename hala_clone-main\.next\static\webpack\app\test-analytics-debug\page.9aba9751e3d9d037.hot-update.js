"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-analytics-debug/page",{

/***/ "(app-pages-browser)/./app/test-analytics-debug/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-analytics-debug/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAnalyticsDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestAnalyticsDebug() {\n    var _authState_user, _authState_user1;\n    _s();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analyticsResult, setAnalyticsResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allProfiles, setAllProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestAnalyticsDebug.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"TestAnalyticsDebug.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            setAuthState(session);\n            if (session) {\n                // Get user profile - try without .single() first to see all results\n                const { data: profileData, error } = await supabase.from('profiles').select('*').eq('id', session.user.id);\n                console.log('Profile query result:', {\n                    profileData,\n                    error\n                });\n                console.log('User ID:', session.user.id);\n                if (error) {\n                    console.error('Profile error:', error);\n                    setProfile({\n                        error: error.message\n                    });\n                } else if (!profileData || profileData.length === 0) {\n                    console.log('No profile found for user');\n                    setProfile({\n                        error: 'No profile found'\n                    });\n                } else {\n                    console.log('Profile found:', profileData[0]);\n                    setProfile(profileData[0]);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n        }\n    };\n    const testAnalytics = async ()=>{\n        setLoading(true);\n        try {\n            console.log('Testing analytics function...');\n            // Get current session\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            if (!session) {\n                setAnalyticsResult({\n                    error: 'No session found'\n                });\n                return;\n            }\n            console.log('Session found, testing direct fetch...');\n            // Test direct fetch to Edge Function\n            const response = await fetch(\"\".concat(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"/functions/v1/dashboard-analytics?timeRange=30d\"), {\n                method: 'GET',\n                headers: {\n                    'Authorization': \"Bearer \".concat(session.access_token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log('Response status:', response.status);\n            console.log('Response headers:', Object.fromEntries(response.headers.entries()));\n            const result = await response.json();\n            console.log('Response data:', result);\n            setAnalyticsResult({\n                status: response.status,\n                ok: response.ok,\n                data: result\n            });\n        } catch (error) {\n            console.error('Analytics test error:', error);\n            setAnalyticsResult({\n                error: error.message\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                console.error('Login error:', error);\n            } else {\n                console.log('Login successful:', data);\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n        }\n    };\n    const createProfile = async ()=>{\n        try {\n            if (!(authState === null || authState === void 0 ? void 0 : authState.user)) {\n                alert('Please login first');\n                return;\n            }\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // First try to update existing profile\n            const { data: updateData, error: updateError } = await supabase.from('profiles').update({\n                email: authState.user.email || '<EMAIL>',\n                full_name: 'Test Business User',\n                role: 'business',\n                business_name: 'Test Business',\n                business_vat_number: '123456789',\n                updated_at: new Date().toISOString()\n            }).eq('id', authState.user.id).select();\n            if (updateError) {\n                console.log('Update failed, trying insert:', updateError);\n                // If update fails, try insert\n                const { data: insertData, error: insertError } = await supabase.from('profiles').insert({\n                    id: authState.user.id,\n                    email: authState.user.email || '<EMAIL>',\n                    full_name: 'Test Business User',\n                    role: 'business',\n                    business_name: 'Test Business',\n                    business_vat_number: '123456789'\n                }).select();\n                if (insertError) {\n                    console.error('Profile insert error:', insertError);\n                    alert(\"Error creating profile: \".concat(insertError.message));\n                } else {\n                    console.log('Profile created:', insertData);\n                    alert('Profile created successfully!');\n                    checkAuth();\n                }\n            } else {\n                console.log('Profile updated:', updateData);\n                alert('Profile updated successfully!');\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Profile creation error:', error);\n        }\n    };\n    const checkAllProfiles = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.from('profiles').select('*');\n            if (error) {\n                console.error('Error fetching all profiles:', error);\n                setAllProfiles({\n                    error: error.message\n                });\n            } else {\n                console.log('All profiles:', data);\n                setAllProfiles(data);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Analytics Debug Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Authentication Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Authenticated:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    authState ? 'Yes' : 'No'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"User ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user = authState.user) === null || _authState_user === void 0 ? void 0 : _authState_user.id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user1 = authState.user) === null || _authState_user1 === void 0 ? void 0 : _authState_user1.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Access Token:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            authState.access_token ? 'Present' : 'Missing'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    !authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: login,\n                        className: \"mt-4\",\n                        children: \"Login with Test Credentials\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            profile && !profile.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"User Profile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Role:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.role\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Full Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.full_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Business Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.business_name || 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this),\n            (profile === null || profile === void 0 ? void 0 : profile.error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Profile Issue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 41\n                                    }, this),\n                                    \" \",\n                                    profile.error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"It looks like your user account doesn't have a profile record. This is needed for the analytics function.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: createProfile,\n                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                children: \"Create Business Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Analytics Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: testAnalytics,\n                        disabled: loading || !authState,\n                        className: \"mb-4\",\n                        children: loading ? 'Testing...' : 'Test Analytics Function'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    analyticsResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold mb-2\",\n                                children: \"Result:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-4 rounded text-sm overflow-auto\",\n                                children: JSON.stringify(analyticsResult, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAnalyticsDebug, \"V43PPzKW07aFODswd40rEm14MTs=\");\n_c = TestAnalyticsDebug;\nvar _c;\n$RefreshReg$(_c, \"TestAnalyticsDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-analytics-debug/page.tsx\n"));

/***/ })

});