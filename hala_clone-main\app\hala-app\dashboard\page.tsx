"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { ArrowUpRight, Users, Coins, BarChart3, TrendingUp, Activity, Loader2 } from "lucide-react"
import { PageTransition } from "@/components/page-transition"
import { useToast } from "@/components/ui/use-toast"

interface UserStats {
  tokensCount: number
  totalUsers: number
  businessUsers: number
  recentActivity: any[]
  marketOverview: { date: string; count: number }[]
}

export default function Dashboard() {
  const [stats, setStats] = useState<UserStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    fetchUserStats()
  }, [])

  const fetchUserStats = async () => {
    try {
      // Import Edge Functions client
      const { edgeFunctions } = await import('@/lib/edge-functions')

      const data = await edgeFunctions.getMyItems()

      if (data.success) {
        // Calculate user stats from items
        const items = data.nfts || []
        setStats({
          totalItems: items.length,
          tokensCount: items.length, // Your tokens count
          totalUsers: 1, // Placeholder - could be calculated from unique owners
          businessUsers: items.filter(item => item.brand).length, // Items with brands as "projects"
          totalTransfers: 0, // Could be calculated from transfer history
          totalValue: 0, // Placeholder
          recentActivity: [], // Placeholder
          marketOverview: [], // Placeholder
        })
      } else {
        throw new Error(data.error || "Failed to fetch dashboard stats")
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load dashboard data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <PageTransition>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </PageTransition>
    )
  }

  const dynamicStats = [
    {
      name: "Total Users",
      value: (stats?.totalUsers ?? 0).toLocaleString(),
      change: "+12%",
      icon: <Users className="h-5 w-5 text-blue-500" />,
      color: "bg-blue-50",
    },
    {
      name: "Your Tokens",
      value: (stats?.tokensCount ?? 0).toLocaleString(),
      change: "+23%",
      icon: <Coins className="h-5 w-5 text-purple-500" />,
      color: "bg-purple-50",
    },
    {
      name: "Active Projects",
      value: (stats?.businessUsers ?? 0).toLocaleString(),
      change: "+7%",
      icon: <BarChart3 className="h-5 w-5 text-green-500" />,
      color: "bg-green-50",
    },
  ]

  return (
    <PageTransition>
      <div className="space-y-6 md:space-y-8">
        <div>
          <h1 className="text-2xl md:text-3xl font-light">Dashboard</h1>
          <p className="text-gray-500 mt-2 text-sm md:text-base">Welcome to your HALA dashboard</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6">
          {dynamicStats.map((stat, index) => (
            <Card
              key={index}
              className="p-4 md:p-6 hover:shadow-lg transition-all duration-300 overflow-hidden relative"
            >
              <div className="flex items-center justify-between relative z-10">
                <div className={`p-2 rounded-full ${stat.color}`}>{stat.icon}</div>
                <span className="text-green-500 flex items-center text-xs md:text-sm bg-green-50 px-2 py-1 rounded-full">
                  {stat.change}
                  <ArrowUpRight className="h-3 w-3 md:h-4 md:w-4 ml-1" />
                </span>
              </div>
              <div className="mt-3 md:mt-4 relative z-10">
                <h3 className="text-gray-500 text-xs md:text-sm">{stat.name}</h3>
                <p className="text-xl md:text-3xl font-light mt-1">{stat.value}</p>
              </div>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-4 md:p-6 overflow-hidden relative">
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg md:text-xl font-light">Recent Activity</h2>
                <div className="p-2 bg-gray-50 rounded-full">
                  <Activity className="h-4 w-4 text-gray-400" />
                </div>
              </div>
              <div className="space-y-3 md:space-y-4">
                {stats?.recentActivity && stats.recentActivity.length > 0 ? (
                  stats.recentActivity.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between py-2 md:py-3 border-b border-gray-100 last:border-0 hover:bg-gray-50 rounded-lg transition-colors px-2"
                    >
                      <div>
                        <p className="text-xs md:text-sm text-gray-900 font-medium">
                          {item.nftName || `Token #${item.nftId.toString().substring(0, 6)}`}
                        </p>
                        <p className="text-xs text-gray-500">{new Date(item.transferredAt).toLocaleString()}</p>
                      </div>
                      <span className="text-xs md:text-sm text-gray-600 font-medium hover:text-black transition-colors">
                        {item.senderId.toString() === item.recipientId.toString() ? "Created" : "Transferred"}
                      </span>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-gray-500">No recent activity</div>
                )}
              </div>
            </div>
          </Card>

          <Card className="p-4 md:p-6 overflow-hidden relative">
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg md:text-xl font-light">Market Overview</h2>
                <div className="p-2 bg-gray-50 rounded-full">
                  <TrendingUp className="h-4 w-4 text-gray-400" />
                </div>
              </div>

              <div className="h-48 flex items-center justify-center">
                <div className="w-full h-32 relative">
                  <div className="absolute bottom-0 left-0 right-0 flex items-end justify-between h-full">
                    {stats?.marketOverview && stats.marketOverview.length > 0
                      ? stats.marketOverview.slice(0, 7).map((day, index) => {
                          const maxValue = Math.max(...stats.marketOverview.map((d) => d.count), 1)
                          const heightPercentage = (day.count / maxValue) * 100

                          return (
                            <div key={index} className="w-1/8 mx-1">
                              <div
                                className="bg-black rounded-t-sm transition-all duration-500 hover:bg-gray-800"
                                style={{ height: `${heightPercentage || 5}%` }}
                                title={`${day.date}: ${day.count} NFTs`}
                              ></div>
                            </div>
                          )
                        })
                      :
                        [35, 60, 45, 75, 50, 80, 65].map((height, index) => (
                          <div key={index} className="w-1/8 mx-1">
                            <div className="bg-gray-200 rounded-t-sm" style={{ height: `${height}%` }}></div>
                          </div>
                        ))}
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-400 pt-2 border-t border-gray-100">
                    {stats?.marketOverview && stats.marketOverview.length > 0
                      ? stats.marketOverview
                          .slice(0, 7)
                          .map((day, index) => (
                            <div key={index}>
                              {new Date(day.date).toLocaleDateString(undefined, { weekday: "short" })}
                            </div>
                          ))
                      : ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day, index) => (
                          <div key={index}>{day}</div>
                        ))}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </PageTransition>
  )
}
