"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-analytics-debug/page",{

/***/ "(app-pages-browser)/./app/test-analytics-debug/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-analytics-debug/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAnalyticsDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestAnalyticsDebug() {\n    var _authState_user, _authState_user1;\n    _s();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analyticsResult, setAnalyticsResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestAnalyticsDebug.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"TestAnalyticsDebug.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            setAuthState(session);\n            if (session) {\n                // Get user profile\n                const { data: profileData, error } = await supabase.from('profiles').select('*').eq('id', session.user.id).single();\n                if (!error) {\n                    setProfile(profileData);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n        }\n    };\n    const testAnalytics = async ()=>{\n        setLoading(true);\n        try {\n            console.log('Testing analytics function...');\n            // Get current session\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            if (!session) {\n                setAnalyticsResult({\n                    error: 'No session found'\n                });\n                return;\n            }\n            console.log('Session found, testing direct fetch...');\n            // Test direct fetch to Edge Function\n            const response = await fetch(\"\".concat(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"/functions/v1/dashboard-analytics?timeRange=30d\"), {\n                method: 'GET',\n                headers: {\n                    'Authorization': \"Bearer \".concat(session.access_token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log('Response status:', response.status);\n            console.log('Response headers:', Object.fromEntries(response.headers.entries()));\n            const result = await response.json();\n            console.log('Response data:', result);\n            setAnalyticsResult({\n                status: response.status,\n                ok: response.ok,\n                data: result\n            });\n        } catch (error) {\n            console.error('Analytics test error:', error);\n            setAnalyticsResult({\n                error: error.message\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                console.error('Login error:', error);\n            } else {\n                console.log('Login successful:', data);\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Analytics Debug Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Authentication Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Authenticated:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    authState ? 'Yes' : 'No'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"User ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user = authState.user) === null || _authState_user === void 0 ? void 0 : _authState_user.id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user1 = authState.user) === null || _authState_user1 === void 0 ? void 0 : _authState_user1.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Access Token:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            authState.access_token ? 'Present' : 'Missing'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    !authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: login,\n                        className: \"mt-4\",\n                        children: \"Login with Test Credentials\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"User Profile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Role:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.role\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Full Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.full_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Business Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.business_name || 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Analytics Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: testAnalytics,\n                        disabled: loading || !authState,\n                        className: \"mb-4\",\n                        children: loading ? 'Testing...' : 'Test Analytics Function'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    analyticsResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold mb-2\",\n                                children: \"Result:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-4 rounded text-sm overflow-auto\",\n                                children: JSON.stringify(analyticsResult, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAnalyticsDebug, \"qrLBCFpXGumtBD54+W+vEyxYn7g=\");\n_c = TestAnalyticsDebug;\nvar _c;\n$RefreshReg$(_c, \"TestAnalyticsDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-analytics-debug/page.tsx\n"));

/***/ })

});