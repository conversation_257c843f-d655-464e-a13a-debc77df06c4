"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/hala-app/business/dashboard/page",{

/***/ "(app-pages-browser)/./app/hala-app/business/dashboard/page.tsx":
/*!**************************************************!*\
  !*** ./app/hala-app/business/dashboard/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BusinessDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,DollarSign,Globe,Loader2,RefreshCw,ShoppingBag,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_page_transition__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/page-transition */ \"(app-pages-browser)/./components/page-transition.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction BusinessDashboard() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BusinessDashboard.useEffect\": ()=>{\n            fetchBusinessStats();\n        }\n    }[\"BusinessDashboard.useEffect\"], []);\n    const fetchBusinessStats = async ()=>{\n        try {\n            // Import Edge Functions client\n            const { edgeFunctions } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_edge-functions_ts-_89a61\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/edge-functions */ \"(app-pages-browser)/./lib/edge-functions.ts\"));\n            const data = await edgeFunctions.getAnalytics('30d');\n            if (data.success) {\n                // Map analytics data to stats format for backward compatibility\n                const analytics = data.analytics;\n                setStats({\n                    totalItems: analytics.itemsSold || 0,\n                    totalTransfers: analytics.itemsSold || 0,\n                    totalCustomers: analytics.newCustomers || 0,\n                    totalRevenue: analytics.totalRevenue || 0,\n                    // Add the properties that the UI expects\n                    tokenizedItems: analytics.itemsSold || 0,\n                    activeCustomers: analytics.newCustomers || 0,\n                    recentTrades: [],\n                    tradesPerNft: {},\n                    topCollections: []\n                });\n            } else {\n                throw new Error(data.error || \"Failed to fetch business stats\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching business stats:\", error);\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to load business data\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshData = ()=>{\n        setIsRefreshing(true);\n        fetchBusinessStats().finally(()=>{\n            setIsRefreshing(false);\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_6__.PageTransition, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    var _stats_tokenizedItems, _stats_activeCustomers;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_transition__WEBPACK_IMPORTED_MODULE_6__.PageTransition, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl md:text-3xl font-light\",\n                                    children: \"Business Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mt-2 text-sm md:text-base\",\n                                    children: \"Monitor the performance of your tokenized assets\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: refreshData,\n                            disabled: isRefreshing,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2 \".concat(isRefreshing ? \"animate-spin\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                    defaultValue: \"overview\",\n                    className: \"space-y-6\",\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"trades\",\n                                    children: \"Trades\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                    value: \"trends\",\n                                    children: \"Trends\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"overview\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        {\n                                            name: \"Tokenized Items\",\n                                            value: ((_stats_tokenizedItems = stats === null || stats === void 0 ? void 0 : stats.tokenizedItems) !== null && _stats_tokenizedItems !== void 0 ? _stats_tokenizedItems : 0).toString(),\n                                            change: \"+12%\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 25\n                                            }, this),\n                                            color: \"bg-blue-50\"\n                                        },\n                                        {\n                                            name: \"Active Customers\",\n                                            value: ((_stats_activeCustomers = stats === null || stats === void 0 ? void 0 : stats.activeCustomers) !== null && _stats_activeCustomers !== void 0 ? _stats_activeCustomers : 0).toString(),\n                                            change: \"+23%\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 25\n                                            }, this),\n                                            color: \"bg-purple-50\"\n                                        },\n                                        {\n                                            name: \"Total Value\",\n                                            value: \"$0\",\n                                            change: \"+18%\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 25\n                                            }, this),\n                                            color: \"bg-green-50\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-full \".concat(stat.color),\n                                                            children: stat.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 flex items-center text-xs bg-green-50 px-2 py-1 rounded-full\",\n                                                            children: [\n                                                                stat.change,\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 37\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: stat.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-light mt-1\",\n                                                    children: stat.value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"Recent Trades\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-gray-50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: (stats === null || stats === void 0 ? void 0 : stats.recentTrades) && stats.recentTrades.length > 0 ? stats.recentTrades.map((tx, index)=>{\n                                                        var _tx_nftId;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between py-3 border-b border-gray-100 last:border-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: tx.nftName || \"Token #\".concat(((_tx_nftId = tx.nftId) !== null && _tx_nftId !== void 0 ? _tx_nftId : 'unknown').toString().substring(0, 6))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 164,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: new Date(tx.transferredAt).toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 167,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 163,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"$1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-4 text-gray-500\",\n                                                        children: \"No recent trades\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-4 border-t border-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                        href: \"/hala-app/business/trades\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full\",\n                                                            children: \"View all trades\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"Geographic Distribution\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-gray-50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-64 flex items-center justify-center bg-gray-50 rounded-lg mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-300 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"Trade distribution map\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Europe\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"42%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"North America\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"35%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Asia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"23%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"trades\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Number of Trades\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 7 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 30 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"default\",\n                                                        size: \"sm\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Trades\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Last 7 days:\",\n                                                                        \" \",\n                                                                        Math.floor(Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0) * 0.3)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Last 30 days:\",\n                                                                        \" \",\n                                                                        Math.floor(Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0) * 0.7)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-8xl font-light mb-2\",\n                                                    children: Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"real-time updated number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                            value: \"trends\",\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-medium\",\n                                                children: \"Price Trends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 7 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Last 30 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"default\",\n                                                        size: \"sm\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Price Paid\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: [\n                                                            \"$\",\n                                                            Object.values((stats === null || stats === void 0 ? void 0 : stats.tradesPerNft) || {}).reduce((sum, count)=>sum + count, 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 flex items-center justify-center bg-gray-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_DollarSign_Globe_Loader2_RefreshCw_ShoppingBag_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-12 w-12 text-gray-300 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"Price trend chart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Min price: $18\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Avg price: $21\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Max price: $24\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 pt-4 border-t border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium mb-3\",\n                                                children: \"Most Traded Collections\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            (stats === null || stats === void 0 ? void 0 : stats.topCollections) && stats.topCollections.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: stats.topCollections.map((collection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            index === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: \"Most traded: \"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 31\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-500\",\n                                                                                children: [\n                                                                                    index + 1,\n                                                                                    \". \"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 308,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: index === 0 ? \"font-medium\" : \"\",\n                                                                                children: collection.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 310,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium\",\n                                                                        children: [\n                                                                            collection.count,\n                                                                            \" trades (\",\n                                                                            collection.percentage,\n                                                                            \"%)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"\".concat(index === 0 ? \"bg-black\" : \"bg-gray-400\", \" h-1 rounded-full\"),\n                                                                    style: {\n                                                                        width: \"\".concat(collection.percentage, \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4 text-gray-500\",\n                                                children: \"No collection data available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\hala-app\\\\business\\\\dashboard\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessDashboard, \"zqbmixdx5KIbCjX8+LAZ4Ywz+FM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BusinessDashboard;\nvar _c;\n$RefreshReg$(_c, \"BusinessDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/hala-app/business/dashboard/page.tsx\n"));

/***/ })

});