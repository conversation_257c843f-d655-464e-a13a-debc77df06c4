import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createSupabaseClient, createSupabaseAdminClient, corsHeaders, errorResponse, successResponse, getUserFromRequest } from '../_shared/utils.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return errorResponse('Method not allowed', 405)
  }

  try {
    // Get authenticated user
    const { user, profile } = await getUserFromRequest(req)

    if (profile.role !== 'business') {
      return errorResponse('Only business users can create items', 403)
    }

    const { name, description, imageUrl, brand, year, serialNumber } = await req.json()

    if (!name || !imageUrl) {
      return errorResponse('Name and image URL are required', 400)
    }

    const supabase = createSupabaseAdminClient()

    // Create the item
    const { data: item, error: itemError } = await supabase
      .from('items')
      .insert({
        name,
        description,
        image_url: imageUrl,
        brand,
        year,
        serial_number: serialNumber,
        creator_id: user.id,
        owner_id: user.id,
        is_active: true,
      })
      .select()
      .single()

    if (itemError || !item) {
      console.error('Error creating item:', itemError)
      return errorResponse('Failed to create item', 500)
    }

    // Add initial ownership record
    const { error: ownershipError } = await supabase
      .from('ownership_history')
      .insert({
        item_id: item.id,
        user_id: user.id,
        user_name: profile.full_name,
      })

    if (ownershipError) {
      console.error('Error creating ownership record:', ownershipError)
      // Don't fail the request, just log the error
    }

    return successResponse({
      message: 'Item created successfully',
      itemId: item.id,
    })
  } catch (error) {
    console.error('Error creating item:', error)
    if (error.message === 'No authorization header' || error.message === 'Invalid token') {
      return errorResponse('Unauthorized', 401)
    }
    return errorResponse('Internal server error', 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/item-mint' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
