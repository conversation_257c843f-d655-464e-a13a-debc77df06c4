"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-analytics-debug/page",{

/***/ "(app-pages-browser)/./app/test-analytics-debug/page.tsx":
/*!*******************************************!*\
  !*** ./app/test-analytics-debug/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAnalyticsDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TestAnalyticsDebug() {\n    var _authState_user, _authState_user1;\n    _s();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [analyticsResult, setAnalyticsResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allProfiles, setAllProfiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestAnalyticsDebug.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"TestAnalyticsDebug.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            setAuthState(session);\n            if (session) {\n                // Get user profile - try without .single() first to see all results\n                const { data: profileData, error } = await supabase.from('profiles').select('*').eq('id', session.user.id);\n                console.log('Profile query result:', {\n                    profileData,\n                    error\n                });\n                console.log('User ID:', session.user.id);\n                if (error) {\n                    console.error('Profile error:', error);\n                    setProfile({\n                        error: error.message\n                    });\n                } else if (!profileData || profileData.length === 0) {\n                    console.log('No profile found for user');\n                    setProfile({\n                        error: 'No profile found'\n                    });\n                } else {\n                    console.log('Profile found:', profileData[0]);\n                    setProfile(profileData[0]);\n                }\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n        }\n    };\n    const testAnalytics = async ()=>{\n        setLoading(true);\n        try {\n            console.log('Testing analytics function...');\n            // Get current session\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data: { session } } = await supabase.auth.getSession();\n            if (!session) {\n                setAnalyticsResult({\n                    error: 'No session found'\n                });\n                return;\n            }\n            console.log('Session found, testing direct fetch...');\n            // Test direct fetch to Edge Function\n            const response = await fetch(\"\".concat(\"https://dcdslxzhypxpledhkvtw.supabase.co\", \"/functions/v1/dashboard-analytics?timeRange=30d\"), {\n                method: 'GET',\n                headers: {\n                    'Authorization': \"Bearer \".concat(session.access_token),\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log('Response status:', response.status);\n            console.log('Response headers:', Object.fromEntries(response.headers.entries()));\n            const result = await response.json();\n            console.log('Response data:', result);\n            setAnalyticsResult({\n                status: response.status,\n                ok: response.ok,\n                data: result\n            });\n        } catch (error) {\n            console.error('Analytics test error:', error);\n            setAnalyticsResult({\n                error: error.message\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email: '<EMAIL>',\n                password: '#rafaEl21'\n            });\n            if (error) {\n                console.error('Login error:', error);\n            } else {\n                console.log('Login successful:', data);\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n        }\n    };\n    const createProfile = async ()=>{\n        try {\n            if (!(authState === null || authState === void 0 ? void 0 : authState.user)) {\n                alert('Please login first');\n                return;\n            }\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            // First try to update existing profile\n            const { data: updateData, error: updateError } = await supabase.from('profiles').update({\n                email: authState.user.email || '<EMAIL>',\n                full_name: 'Test Business User',\n                role: 'business',\n                business_name: 'Test Business',\n                business_vat_number: '123456789',\n                updated_at: new Date().toISOString()\n            }).eq('id', authState.user.id).select();\n            if (updateError) {\n                console.log('Update failed, trying insert:', updateError);\n                // If update fails, try insert\n                const { data: insertData, error: insertError } = await supabase.from('profiles').insert({\n                    id: authState.user.id,\n                    email: authState.user.email || '<EMAIL>',\n                    full_name: 'Test Business User',\n                    role: 'business',\n                    business_name: 'Test Business',\n                    business_vat_number: '123456789'\n                }).select();\n                if (insertError) {\n                    console.error('Profile insert error:', insertError);\n                    alert(\"Error creating profile: \".concat(insertError.message));\n                } else {\n                    console.log('Profile created:', insertData);\n                    alert('Profile created successfully!');\n                    checkAuth();\n                }\n            } else {\n                console.log('Profile updated:', updateData);\n                alert('Profile updated successfully!');\n                checkAuth();\n            }\n        } catch (error) {\n            console.error('Profile creation error:', error);\n        }\n    };\n    const checkAllProfiles = async ()=>{\n        try {\n            const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)();\n            const { data, error } = await supabase.from('profiles').select('*');\n            if (error) {\n                console.error('Error fetching all profiles:', error);\n                setAllProfiles({\n                    error: error.message\n                });\n            } else {\n                console.log('All profiles:', data);\n                setAllProfiles(data);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Analytics Debug Page\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Authentication Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Authenticated:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" \",\n                                    authState ? 'Yes' : 'No'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"User ID:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user = authState.user) === null || _authState_user === void 0 ? void 0 : _authState_user.id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Email:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            (_authState_user1 = authState.user) === null || _authState_user1 === void 0 ? void 0 : _authState_user1.email\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Access Token:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 18\n                                            }, this),\n                                            \" \",\n                                            authState.access_token ? 'Present' : 'Missing'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    !authState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: login,\n                        className: \"mt-4\",\n                        children: \"Login with Test Credentials\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            profile && !profile.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"User Profile\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Role:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.role\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Full Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.full_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Business Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    profile.business_name || 'N/A'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this),\n            (profile === null || profile === void 0 ? void 0 : profile.error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Profile Issue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 41\n                                    }, this),\n                                    \" \",\n                                    profile.error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"It looks like your user account doesn't have a profile record. This is needed for the analytics function.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: createProfile,\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        children: \"Update/Create Business Profile\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: checkAllProfiles,\n                                        variant: \"outline\",\n                                        children: \"Check All Profiles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Analytics Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: testAnalytics,\n                        disabled: loading || !authState,\n                        className: \"mb-4\",\n                        children: loading ? 'Testing...' : 'Test Analytics Function'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    analyticsResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold mb-2\",\n                                children: \"Result:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-4 rounded text-sm overflow-auto\",\n                                children: JSON.stringify(analyticsResult, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            allProfiles && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"All Profiles Debug\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-gray-100 p-4 rounded text-sm overflow-auto max-h-64\",\n                        children: JSON.stringify(allProfiles, null, 2)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop Source\\\\Solvpro\\\\Hala\\\\hala_clone-main\\\\app\\\\test-analytics-debug\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAnalyticsDebug, \"V43PPzKW07aFODswd40rEm14MTs=\");\n_c = TestAnalyticsDebug;\nvar _c;\n$RefreshReg$(_c, \"TestAnalyticsDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-analytics-debug/page.tsx\n"));

/***/ })

});